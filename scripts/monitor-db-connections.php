<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    // Get current connection count
    $connectionCount = DB::select("SELECT count(*) as count FROM pg_stat_activity")[0]->count;
    
    // Get max connections
    $maxConnections = DB::select("SHOW max_connections")[0]->max_connections;
    
    // Get connections by application
    $connectionsByApp = DB::select("
        SELECT 
            application_name, 
            count(*) as count,
            state
        FROM pg_stat_activity 
        WHERE application_name IS NOT NULL
        GROUP BY application_name, state
        ORDER BY count DESC
    ");
    
    // Get idle connections
    $idleConnections = DB::select("
        SELECT count(*) as count 
        FROM pg_stat_activity 
        WHERE state = 'idle'
    ")[0]->count;
    
    echo "=== PostgreSQL Connection Monitor ===\n";
    echo "Current connections: {$connectionCount}/{$maxConnections}\n";
    echo "Idle connections: {$idleConnections}\n";
    echo "Usage: " . round(($connectionCount / $maxConnections) * 100, 2) . "%\n\n";
    
    echo "Connections by application:\n";
    foreach ($connectionsByApp as $app) {
        echo "  {$app->application_name} ({$app->state}): {$app->count}\n";
    }
    
    // Warning if usage is high
    $usagePercent = ($connectionCount / $maxConnections) * 100;
    if ($usagePercent > 80) {
        echo "\n⚠️  WARNING: High connection usage ({$usagePercent}%)\n";
    }
    
} catch (Exception $e) {
    echo "Error monitoring connections: " . $e->getMessage() . "\n";
    exit(1);
}

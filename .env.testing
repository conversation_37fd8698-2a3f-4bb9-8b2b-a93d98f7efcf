APP_NAME="ZTS"
APP_ENV=testing
APP_KEY=base64:YYMNun3DfczjbnzDnLnhItNLLKbbpwKCARWq77AXCpQ=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=https://zts.test

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=localhost
# DB_PORT=5432
DB_DATABASE=:memory:
# DB_USERNAME=postgres
# DB_PASSWORD=root

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=reverb
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

CACHE_STORE=redis
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

REVERB_APP_ID=527674
REVERB_APP_KEY=vxnqth7yo6uvqt4iwc6a
REVERB_APP_SECRET=7sqom4uvwnamwvpi1hsa
REVERB_HOST="rt.zts.test"
REVERB_PORT=8080
REVERB_SCHEME=https
REVERB_SERVER_HOST="127.0.0.1"
REVERB_VERIFY_SSL=false
REVERB_SCALING_ENABLED=true

VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="${REVERB_HOST}"
VITE_REVERB_PORT="443"
VITE_REVERB_SCHEME="${REVERB_SCHEME}"

# MAIL_MAILER=smtp
# MAIL_HOST=sandbox.smtp.mailtrap.io
# MAIL_PORT=2525
# MAIL_USERNAME=e1399b51895eba
# MAIL_PASSWORD=99189a79f76ca1

GOOGLE_MAPS_API_KEY=AIzaSyCuiw7shEGM2iXXCAXNBcdGdLJ2045terk
 
# OTP_BLOCK_DURATION_FIRST=1
# OTP_BLOCK_DURATION_SECOND=3
# OTP_MAX_ATTEMPTS=3

DRIVER_SEARSH_FIRST_RADIUS_FROM=0
DRIVER_SEARSH_FIRST_RADIUS_TO=5
FIRST_RADIUS_TIMEOUT=60
DRIVER_SEARSH_SECONDE_RADIUS_FROM=5
DRIVER_SEARSH_SECONDE_RADIUS_TO=8
SECONDE_RADIUS_TIMEOUT=30
DRIVER_SEARSH_THIRD_RADIUS_FROM=8
DRIVER_SEARSH_THIRD_RADIUS_TO=10
THIRD_RADIUS_TIMEOUT=20
 
MAX_CANCELLATIONS="4"
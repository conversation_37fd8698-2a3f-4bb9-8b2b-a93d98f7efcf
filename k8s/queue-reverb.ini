[program:queue_worker]
command=/bin/sh -c "echo 'starting queue:work'; php /var/www/html/artisan queue:work --max-jobs=100 --max-time=3600 --memory=512 --timeout=300"
autostart=true
priority=15
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true

[program:reverb]
command=/bin/sh -c "echo 'starting reverb:start'; php /var/www/html/artisan reverb:start"
autostart=true
priority=20
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true

